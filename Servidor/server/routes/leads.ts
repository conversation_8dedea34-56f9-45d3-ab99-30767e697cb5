import {Router} from 'express';
import MapeadorDeLead from '../mapeadores/MapeadorDeLead';
import {Resposta} from '../utils/Resposta';
import Lead, { OrigemLead, InstagramData } from '../domain/crm/Lead';
import { CrmEmpresa } from '../domain/crm/CrmEmpresa';
import MapeadorDeCrmEmpresa from '../mapeadores/MapeadorDeCrmEmpresa';

const router: Router = Router();
const axios = require('axios');


// Rota dadosig que busca dados do Instagram
router.get('/dadosig', async (req: any, res) => {
  try {
    const { username, crmEmpresaId } = req.query;

    if (!username) {
      return res.json(Resposta.erro('Username do Instagram é obrigatório'));
    }
    // crmEmpresaId agora é opcional – se não vier será criado/selecionado automaticamente

    console.log('Buscando dados do Instagram para:', username);

    const url = `https://i.instagram.com/api/v1/users/web_profile_info/?username=${username}`;

    // Configuração do proxy
    const proxyConfig = {
      host: 'proxy.toolip.io',
      port: 31114,
      auth: {
        username: 'tl-64be0af36dacbe390da7ea18f17c3631b12ef9278cd5d5befbbc79f615e02206-country-BR-session-1',
        password: 'pmxs1r1880t0'
      },
      protocol: 'http'
    };

    const resposta = await axios.get(url, {
      proxy: proxyConfig,
      headers: {
        "x-ig-app-id": "936619743392459",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/62.0.3202.94 Safari/537.36",
        "Accept-Language": "en-US,en;q=0.9,ru;q=0.8",
        "Accept": "*/*",
      },
      timeout: 30000, // 30 segundos de timeout
      validateStatus: function (status: number) {
        return status < 500; // Resolve apenas se o status for menor que 500
      }
    });

    const data = resposta.data;
    console.log('Resposta Instagram API:', data);

    const user = data?.data?.user;

    // Debug dos bio_links
    if (user?.bio_links) {
      console.log('Bio Links encontrados:', JSON.stringify(user.bio_links, null, 2));
    }
    if (!user) {
      console.error('Resposta inesperada da API do Instagram:', data);
      return res.json(Resposta.erro('Não foi possível encontrar usuário no Instagram'));
    }

    // Obter ou criar empresa
    let crmEmpresaIdNumeric: number;
    let empresaObj: CrmEmpresa;

    if (crmEmpresaId) {
      crmEmpresaIdNumeric = parseInt(crmEmpresaId as string, 10);
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();
      empresaObj = await mapeadorCrmEmpresa.selecioneSync({ id: crmEmpresaIdNumeric });
    } else {
      const nomeEmpresa = user.full_name || user.username;
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      // Tenta encontrar empresa existente pelo nome (primeira ocorrência)
      const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: nomeEmpresa });

      if (existente && existente.id) {
        crmEmpresaIdNumeric = existente.id;
        empresaObj = existente;
      } else {
        const novaEmpresa = new CrmEmpresa(nomeEmpresa);
        // Preencher dados básicos quando disponíveis
        if (user.business_phone_number) novaEmpresa.telefone = user.business_phone_number;
        if (user.business_email) novaEmpresa.email = user.business_email;
        if (user.business_address_json) novaEmpresa.endereco = JSON.stringify(user.business_address_json);

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);

        crmEmpresaIdNumeric = criada.id;
        empresaObj = novaEmpresa;
      }
    }

    // Montar objeto instagramData conforme nosso domínio
    const instagramData: InstagramData = {
      bio: user.biography,
      followers: user.edge_followed_by?.count,
      following: user.edge_follow?.count,
      accountType: (user.is_business_account ? 'Business' : 'Pessoal') as 'Business' | 'Pessoal',
      businessCategory: user.business_category_name || user.category_name,
      location: user.business_address_json ? JSON.stringify(user.business_address_json) : undefined,
      website: user.external_url,
      lastPhotos: user.edge_owner_to_timeline_media?.edges?.slice(0, 9).map((edge: any) => edge.node.display_url) || []
    };

    // Processar bio_links se existir
    let observacoesBioLinks = '';
    if (user.bio_links && Array.isArray(user.bio_links) && user.bio_links.length > 0) {
      const links = user.bio_links.map((link: any, index: number) => {
        if (typeof link === 'string') {
          return `${index + 1}. ${link}`;
        } else if (link && link.url) {
          const titulo = link.title || link.text || 'Link';
          return `${index + 1}. ${titulo}: ${link.url}`;
        }
        return `${index + 1}. ${JSON.stringify(link)}`;
      });
      observacoesBioLinks = `Bio Links do Instagram:\n${links.join('\n')}`;
    }

    // Criar Lead parcialmente preenchido
    const lead = new Lead(
      crmEmpresaIdNumeric,
      user.full_name || user.username,
      user.full_name || user.username,
      user.business_phone_number || '',
      user.username,
      user.biography || undefined,
      OrigemLead.Instagram
    );
    lead.instagramData = instagramData;
    lead.linkInsta = user.external_url || null; // Link da bio do Instagram
    lead.notas = observacoesBioLinks || undefined; // Adicionar bio_links nas observações
    if (empresaObj) lead.crmEmpresa = empresaObj;

    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao buscar dados do Instagram:', err);
    res.json(Resposta.erro('Erro ao buscar dados do Instagram: ' + err.message));
  }
});

// Listagem com filtros e paginação
router.get('/', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const params = {
      inicio: req.query.inicio ? parseInt(req.query.inicio) : 0,
      total: req.query.total ? parseInt(req.query.total) : 20,
      texto: req.query.texto || null,
      etapa: req.query.etapa || null,
      crmEmpresaId: req.query.crmEmpresaId || null
    };

    const dados = await mapeador.listeAsync(params);
    const total = await mapeador.selecioneTotal(params);
    res.json(Resposta.sucesso({ data: dados, total }));
  } catch (err) {
    console.error('Erro ao listar leads', err);
    res.json(Resposta.erro('Erro ao listar leads'));
  }
});

// Selecionar um lead
router.get('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const lead = await mapeador.selecioneSync({ id: req.params.id });
    if (!lead) return res.json(Resposta.erro('Lead não encontrado'));
    res.json(Resposta.sucesso(lead));
  } catch (err) {
    console.error('Erro ao obter lead', err);
    res.json(Resposta.erro('Erro ao obter lead'));
  }
});

// Inserir lead
router.post('/', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const obj = req.body;
    console.log('Dados recebidos para inserir lead:', JSON.stringify(obj, null, 2));

    // Se não tiver crmEmpresaId, usar uma empresa padrão ou criar uma nova
    if (!obj.crmEmpresaId) {
      // Usar empresa padrão ou criar uma baseada no nome da empresa do lead
      const mapeadorCrmEmpresa = new MapeadorDeCrmEmpresa();

      if (obj.empresa) {
        // Tenta encontrar empresa existente pelo nome
        const existente = await mapeadorCrmEmpresa.selecioneSync({ nome: obj.empresa });

        if (existente && existente.id) {
          obj.crmEmpresaId = existente.id;
        } else {
          // Criar nova empresa
          const novaEmpresa = new CrmEmpresa(obj.empresa);
          if (obj.telefone) novaEmpresa.telefone = obj.telefone;

          const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
          obj.crmEmpresaId = criada.id;
        }
      } else {
        // Criar empresa com nome genérico baseado no responsável
        const nomeEmpresa = `${obj.nomeResponsavel || 'Lead'} - Instagram`;
        const novaEmpresa = new CrmEmpresa(nomeEmpresa);
        if (obj.telefone) novaEmpresa.telefone = obj.telefone;

        const criada = await mapeadorCrmEmpresa.insiraSync(novaEmpresa);
        obj.crmEmpresaId = criada.id;
      }
    }

    const novo = await mapeador.insiraSync(obj);
    res.json(Resposta.sucesso(novo));
  } catch (err) {
    console.error('Erro ao inserir lead', err);
    res.json(Resposta.erro('Erro ao inserir lead: ' + err.message));
  }
});

// Atualizar lead
router.put('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    const obj = req.body;
    obj.id = req.params.id;
    const atualizado = await mapeador.atualizeSync(obj);
    res.json(Resposta.sucesso(atualizado));
  } catch (err) {
    console.error('Erro ao atualizar lead', err);
    res.json(Resposta.erro('Erro ao atualizar lead'));
  }
});

// Remover lead
router.delete('/:id', async (req: any, res) => {
  const mapeador = new MapeadorDeLead();
  try {
    await mapeador.removaAsync({ id: req.params.id });
    res.json(Resposta.sucesso({}));
  } catch (err) {
    console.error('Erro ao remover lead', err);
    res.json(Resposta.erro('Erro ao remover lead'));
  }
});

export const LeadsController: Router = router;
