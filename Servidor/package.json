{"name": "Promokit", "version": "0.0.1", "license": "MIT", "main": "src/main.js", "scripts": {"ng": "ng", "build": "ng build --configuration production", "startAngular": "node --max_old_space_size=140000 ./node_modules/@angular/cli/bin/ng serve sorteieme-js --ssl --configuration=pt", "startAngularLoja": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng serve loja --ssl --port 4201 --configuration=pt", "startAngularAntigo": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --watch --project=\"sorteieme-js\"", "startAngularLojaAntigo": "node --max_old_space_size=8192 ./node_modules/@angular/cli/bin/ng build --watch --project=\"loja\"", "copiar": "grunt watch", "start": "concurrently --kill-others-on-fail \"npm run startTsc\" \"npm run startAngular\" \"npm run startAngularLoja\" \"npm run copiar\"", "startAdmin": "concurrently --kill-others-on-fail \"npm run startTsc\" \"npm run startAngular\" \"npm run copiar\"", "startLoja": "concurrently --kill-others-on-fail \"npm run startTsc\" \"npm run startAngularLoja\" \"npm run copiar\"", "start2": "concurrently --kill-others-on-fail \"npm run startTsc\" \"npm run startAngularAntigo\" \"npm run startAngularLojaAntigo\" \"npm run copiar\"", "admin": "concurrently --kill-others-on-fail \"npm run startTsc\" \"npm run startAngularAntigo\" \"npm run copiar\"", "loja": "concurrently --kill-others-on-fail \"npm run startTsc\" \"npm run startAngularLojaAntigo\" \"npm run copiar\"", "startExpress": "nodemon --watch distServer distServer/bin/www", "startTsc": "tsc --p server --watch", "compilar": "tsc --p server", "test": "mocha -r ts-node/register server/testes/*.spec.js", "lint": "ng lint", "e2e": "ng e2e"}, "private": true, "dependencies": {"@angular/animations": "14.3.0", "@angular/cdk": "14.2.7", "@angular/common": "14.3.0", "@angular/compiler": "14.3.0", "@angular/core": "14.3.0", "@angular/forms": "14.3.0", "@angular/localize": "14.3.0", "@angular/material": "11.2.10", "@angular/platform-browser": "14.3.0", "@angular/platform-browser-dynamic": "14.3.0", "@angular/router": "14.3.0", "@angular/service-worker": "14.3.0", "@google/maps": "^1.1.3", "@mapbox/togeojson": "^0.16.0", "@netcreaties/ngx-smart-banner": "^1.2.3", "@ng-bootstrap/ng-bootstrap": "^13.1.1", "@popperjs/core": "^2.10.2", "@progress/kendo-angular-buttons": "11.0.0", "@progress/kendo-angular-charts": "^5.1.0", "@progress/kendo-angular-common": "11.0.0", "@progress/kendo-angular-conversational-ui": "11.0.0", "@progress/kendo-angular-dateinputs": "11.0.0", "@progress/kendo-angular-dialog": "11.0.0", "@progress/kendo-angular-dropdowns": "11.0.0", "@progress/kendo-angular-excel-export": "11.0.0", "@progress/kendo-angular-grid": "11.0.0", "@progress/kendo-angular-icons": "11.0.0", "@progress/kendo-angular-inputs": "11.0.0", "@progress/kendo-angular-intl": "11.0.0", "@progress/kendo-angular-l10n": "11.0.0", "@progress/kendo-angular-label": "11.0.0", "@progress/kendo-angular-layout": "11.0.0", "@progress/kendo-angular-listview": "11.0.0", "@progress/kendo-angular-messages": "^1.58.0", "@progress/kendo-angular-navigation": "^1.0.0", "@progress/kendo-angular-notification": "11.0.0", "@progress/kendo-angular-pager": "11.0.0", "@progress/kendo-angular-pdf-export": "11.0.0", "@progress/kendo-angular-popup": "11.0.0", "@progress/kendo-angular-progressbar": "11.0.0", "@progress/kendo-angular-scrollview": "^4.0.1", "@progress/kendo-angular-sortable": "11.0.0", "@progress/kendo-angular-tooltip": "^3.0.0", "@progress/kendo-angular-treeview": "11.0.0", "@progress/kendo-angular-upload": "^11.0.0", "@progress/kendo-data-query": "^1.0.0", "@progress/kendo-drawing": "^1.17.2", "@progress/kendo-licensing": "^1.0.2", "@progress/kendo-svg-icons": "^1.0.0", "@progress/kendo-theme-default": "6.0.3", "@sentry/angular-ivy": "^7.114.0", "@sentry/browser": "^5.18.1", "@socket.io/redis-adapter": "^8.3.0", "@turf/boolean-point-in-polygon": "^6.3.0", "@turf/helpers": "^6.3.0", "@turf/turf": "^6.3.0", "@types/bcrypt": "^3.0.0", "@types/bluebird": "^3.5.29", "@types/bull": "^3.10.2", "@types/connect-redis": "0.0.7", "@types/cookie-parser": "^1.4.1", "@types/cors": "^2.8.10", "@types/es6-promise": "^3.3.0", "@types/express-fileupload": "^0.1.1", "@types/express-session": "^1.15.13", "@types/facebook-js-sdk": "^3.3.12", "@types/facebook-nodejs-business-sdk": "22.0.0", "@types/gm": "1.18.3", "@types/google-libphonenumber": "^7.4.20", "@types/jsonwebtoken": "^9.0.7", "@types/mysql": "^2.15.6", "@types/node-jose": "^1.1.13", "@types/passport": "^1.0.0", "@types/passport-local": "^1.0.33", "@types/redis": "^2.8.14", "@types/request-promise-native": "^1.0.16", "@types/sax": "^1.2.1", "@types/serve-favicon": "^2.2.30", "@types/shortid": "0.0.29", "@types/socket.io": "^3.0.2", "@types/swiper": "^4.4.4", "@types/underscore": "^1.9.2", "@types/uuid": "^3.4.5", "@types/xml2js": "^0.4.5", "@zxing/browser": "^0.1.5", "@zxing/library": "^0.21.3", "@zxing/ngx-scanner": "^19.0.0", "actions-on-google": "2.9.1-preview.1", "async": "^3.1.0", "async-lock": "^1.3.0", "aws-sdk": "^2.682.0", "axios": "0.21.1", "axios-retry": "^3.4.0", "base62": "^2.0.1", "bcrypt": "^5.0.0", "blockly": "^9.3.3", "body-parser": "^1.20.2", "bootstrap": "^5.2.0", "bull": "^3.10.0", "chart.js": "^2.8.0", "cheerio": "1.0.0-rc.3", "cldr-data": "^35.1.0", "concurrently": "^5.1.0", "connect-redis": "^3.4.2", "console-stamp": "^0.2.9", "cookie-parser": "^1.4.4", "core-js": "^2.6.9", "cors": "^2.8.5", "country-data": "0.0.31", "country-flag-icons": "^1.5.5", "cron": "^1.7.2", "crypto-js": "^4.1.1", "csv": "^5.4.0", "csv-parse": "4.16.0", "device-detector-js": "^3.0.3", "dexie": "^3.2.3", "diacritics-normalizr": "^1.0.3", "dialogflow": "^1.2.0", "ejs": "^2.7.4", "email-templates": "^7.0.5", "email-validator": "^2.0.4", "express": "4.17.1", "express-fileupload": "^0.4.0", "express-jwt": "^5.3.1", "express-session": "1.17.1", "facebook-nodejs-business-sdk": "^22.0.3", "fast-xml-parser": "^4.3.6", "fb": "^2.0.0", "firebase-admin": "^8.13.0", "geolib": "^3.3.1", "get-intrinsic": "^1.2.4", "gm": "^1.23.1", "google-libphonenumber": "^3.2.32", "hammerjs": "^2.0.8", "i18n": "^0.13.3", "instagram-private-api": "^1.46.1", "iugu": "github:Fibonacci-Solucoes-Ageis/iugu-node", "js-sha256": "^0.9.0", "jsonwebtoken": "^9.0.2", "leaflet": "^1.7.1", "marked": "^5.0.0", "mercadopago": "1.5.17", "metadata-scraper": "^0.2.61", "moment": "^2.24.0", "morgan": "^1.9.1", "mybatisnodejs": "^0.4.6", "mysql": "^2.17.1", "nanoid": "^3.1.12", "ng-click-outside": "^3.3.0", "ng-image-slider": "2.6.4", "ng-inline-svg": "^12.0.0", "ng2-currency-mask": "^9.0.2", "ngx-cookie-service": "^11.0.2", "ngx-countdown": "^3.2.0", "ngx-csv-parser": "0.0.3", "ngx-device-detector": "8.0.0", "ngx-image-cropper": "5.0.0", "ngx-infinite-scroll": "^9.1.0", "ngx-markdown": "11.2.0", "ngx-mask": "^11.1.5", "node-jose": "^2.2.0", "node-uuid": "^1.4.8", "oauth2orize": "^1.11.0", "openai": "^3.2.1", "pagseguro-nodejs": "^0.1.2", "passport": "^0.4.0", "passport-http": "^0.3.0", "passport-http-bearer": "^1.0.1", "passport-local": "^1.0.0", "passport-oauth2-client-password": "^0.1.2", "pb-util": "^0.1.2", "pem": "^1.14.4", "pluralize": "^8.0.0", "puppeteer": "^5.5.0", "qrcode": "^1.4.4", "querystring": "^0.2.0", "rand-token": "^0.4.0", "randomstring": "^1.1.5", "raw-body": "^2.5.2", "rediscache": "^0.2.0", "redlock": "4.2.0", "request": "^2.88.0", "request-promise-native": "^1.0.7", "rxjs": "6.6.7", "serve-favicon": "^2.5.0", "sharp": "^0.26.2", "shortid": "^2.2.14", "slugify": "^1.3.4", "soap": "^0.36.0", "socket.io": "^4.7.5", "socket.io-client": "^4.7.5", "source-map-support": "^0.5.12", "tor-axios": "^1.0.9", "totalvoice-node": "^1.8.0", "tslib": "^2.0.0", "twilio": "^3.37.1", "ua-parser-js": "^1.0.37", "underscore": "^1.9.1", "unescape": "^1.0.1", "unfetch": "^5.0.0", "vanilla-text-mask": "^5.1.1", "xml-crypto": "^2.1.2", "xml-escape": "^1.1.0", "xml-js": "^1.6.11", "xml2js": "^0.4.23", "xmldom": "^0.5.0", "zone.js": "~0.11.4"}, "devDependencies": {"@angular-devkit/build-angular": "^14.2.13", "@angular/cli": "^14.2.13", "@angular/compiler-cli": "14.3.0", "@angular/language-service": "14.3.0", "@progress/kendo-angular-conversational-ui": "^11.0.0", "@types/async": "^3.0.1", "@types/axios": "^0.14.0", "@types/chai": "^4.2.12", "@types/country-data": "0.0.2", "@types/country-flag-icons": "^1.2.0", "@types/cron": "^1.7.1", "@types/googlemaps": "^3.43.3", "@types/jasmine": "~3.6.0", "@types/jasminewd2": "^2.0.6", "@types/marked": "^4.3.0", "@types/mocha": "^8.0.3", "@types/nanoid": "^2.1.0", "@types/node": "^14.18.63", "@types/node-fetch": "^2.6.11", "@types/passport-http": "^0.3.8", "@types/passport-http-bearer": "^1.0.35", "@types/passport-oauth2-client-password": "^0.1.2", "@types/redis": "^2.8.32", "@types/ua-parser-js": "^0.7.39", "chai": "^4.2.0", "codelyzer": "^6.0.0", "csv-write-stream": "^2.0.0", "grunt": "^1.6.1", "grunt-contrib-copy": "^1.0.0", "grunt-contrib-watch": "^1.1.0", "grunt-legacy-util": "^2.0.1", "jasmine-core": "~3.6.0", "jasmine-spec-reporter": "~5.0.0", "karma": "~6.3.2", "karma-chrome-launcher": "~3.1.0", "karma-coverage-istanbul-reporter": "~3.0.2", "karma-jasmine": "~4.0.0", "karma-jasmine-html-reporter": "^1.5.0", "mocha": "^8.1.3", "ng2-account-kit": "^1.0.2", "protractor": "~7.0.0", "ts-node": "^4.1.0", "tslint": "~6.1.0", "typescript": "4.6.4", "uuid": "^3.3.2"}}