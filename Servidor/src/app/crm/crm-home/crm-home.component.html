<div class="crm-home-container">
  <!-- Card de resumo do lead no topo -->
  <div class="lead-card-header" *ngIf="dadosLeadAtual">
    <div class="card-background"></div>
    
    <!-- Layout condensado em uma única linha -->
    <div class="lead-condensed-layout">
      <!-- Avatar e Score -->
      <div class="lead-avatar-section">
        <div class="lead-avatar">
          <i class="fa fa-user"></i>
        </div>
        <div class="score-badge" [ngStyle]="{'background-color': getCorDoScore(dadosLeadAtual?.scoreLead)}">
          {{ formatarScore(dadosLeadAtual?.scoreLead) }}
        </div>
      </div>
      
      <!-- Informações principais -->
      <div class="lead-main-info">
        <div class="lead-header-row">
          <h1 class="lead-name">{{ dadosLeadAtual?.nome || 'Lead sem nome' }}</h1>
          <span class="stage-badge" [ngClass]="dadosLeadAtual?.etapaFunil?.toLowerCase() || ''">{{ dadosLeadAtual?.etapaFunil || 'Indefinida' }}</span>
        </div>
        
        <div class="lead-details-row">
          <span class="detail-item" *ngIf="dadosLeadAtual?.empresa">
            <i class="fa fa-building"></i> {{ dadosLeadAtual?.empresa }}
          </span>
          <span class="detail-item" *ngIf="dadosLeadAtual?.telefone">
            <i class="fa fa-phone"></i> {{ dadosLeadAtual?.telefone }}
          </span>
          <span class="detail-item" *ngIf="dadosLeadAtual?.email">
            <i class="fa fa-envelope"></i> {{ dadosLeadAtual?.email }}
          </span>
        </div>
      </div>
      
      <!-- Redes sociais compactas -->
      <div class="social-compact">
        <a class="social-icon instagram" *ngIf="dadosLeadAtual?.instagram" 
           [href]="'https://instagram.com/' + dadosLeadAtual?.instagram?.replace('@', '')" 
           target="_blank" 
           [title]="dadosLeadAtual?.instagram">
          <i class="fa fa-instagram"></i>
        </a>
        
        <a class="social-icon linkedin" *ngIf="dadosLeadAtual?.linkedin" 
           [href]="'https://' + dadosLeadAtual?.linkedin" 
           target="_blank" 
           title="LinkedIn">
          <i class="fa fa-linkedin"></i>
        </a>
        
        <a class="social-icon website" *ngIf="dadosLeadAtual?.site" 
           [href]="'https://' + dadosLeadAtual?.site" 
           target="_blank" 
           [title]="dadosLeadAtual?.site">
          <i class="fa fa-globe"></i>
        </a>
      </div>
      
      <!-- Informações extras compactas -->
      <div class="extra-info">
        <div class="info-item" *ngIf="dadosLeadAtual?.proximoFollowUp">
          <i class="fa fa-clock-o"></i>
          <span>{{ dadosLeadAtual?.proximoFollowUp }}</span>
        </div>
        <div class="info-item" *ngIf="dadosLeadAtual?.segmento">
          <i class="fa fa-tag"></i>
          <span>{{ dadosLeadAtual?.segmento }}</span>
        </div>
      </div>
      
      <!-- Ações rápidas -->
      <div class="lead-actions">
        <button class="action-button phone" title="Ligar">
          <i class="fa fa-phone"></i>
        </button>
        <button class="action-button email" title="E-mail">
          <i class="fa fa-envelope"></i>
        </button>
        <button class="action-button whatsapp" title="WhatsApp">
          <i class="fa fa-whatsapp"></i>
        </button>
        <button class="action-button calendar" title="Agendar">
          <i class="fa fa-calendar-plus-o"></i>
        </button>
      </div>
    </div>
  </div>
  
  <div class="crm-main-content">
    <div class="sugestoes-panel">
      <app-prompt-sugestoes></app-prompt-sugestoes>
    </div>
  </div>

  <div class="actions-panel">
    <button class="btn btn-primary" (click)="simularNovasMensagens()">
      <i class="fa fa-refresh"></i> Simular Nova Conversa
    </button>
  </div>
</div>
