// Variáveis e cores
$primary: #3b7ddd;
$primary-dark: #2d6ec0;
$primary-light: #e8f2ff;
$accent: #2ecc71;
$accent-light: #e8f8ed;
$neutral: #4a6583;
$neutral-light: #edf1f7;
$warning: #f39c12;
$danger: #e74c3c;
$gray-light: #f8f9fa;
$gray-lighter: #f2f3f5;
$gray-border: #e0e0e0;
$gray-dark: #495057;
$text-dark: #344767;
$text-secondary: #666;
$radius: 8px;
$transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

.crm-home-container {
  padding: 0;
  max-width: 100%;
  height: 100vh;
  margin: 0;
  font-family: 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
  display: flex;
  flex-direction: column;
  background-color: $gray-light;
}

// Cabeçalho principal compacto
.main-header {
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  padding: 12px 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: linear-gradient(90deg, $accent 0%, rgba(255,255,255,0.3) 50%, $accent 100%);
  }
}

.header-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-icon {
  font-size: 24px;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.main-title {
  margin: 0;
  font-size: 20px;
  font-weight: 700;
  letter-spacing: -0.5px;
  color: white !important;
}

.subtitle {
  font-size: 13px;
  opacity: 0.9;
  font-weight: 400;
  margin-left: auto;
  color: white !important;
}

// Lead card no topo - Layout condensado
.lead-card-header {
  background: linear-gradient(135deg, #ffffff 0%, #f8fafe 100%);
  padding: 16px 20px;
  border-bottom: 1px solid $gray-border;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
  min-height: 80px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, $primary 0%, $accent 50%, $primary 100%);
  }
}

.card-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at top right, rgba(59, 125, 221, 0.02) 0%, transparent 50%);
  pointer-events: none;
}

// Layout condensado em linha única
.lead-condensed-layout {
  display: flex;
  align-items: center;
  gap: 20px;
  position: relative;
  z-index: 2;
  flex-wrap: wrap;
}

// Avatar e score compactos
.lead-avatar-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-shrink: 0;
}

.lead-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  background: linear-gradient(135deg, $primary 0%, darken($primary, 10%) 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  box-shadow: 0 3px 8px rgba(59, 125, 221, 0.25);
  border: 2px solid white;
}

// Informações principais
.lead-main-info {
  flex: 1;
  min-width: 250px;
}

.lead-header-row {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 4px;
}

.lead-name {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: $text-dark;
  line-height: 1.2;
}

.lead-details-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.detail-item {
  font-size: 12px;
  color: $text-secondary;
  display: flex;
  align-items: center;
  gap: 4px;
  
  i {
    color: $primary;
    opacity: 0.8;
    font-size: 11px;
  }
}

// Redes sociais compactas
.social-compact {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.social-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  text-decoration: none;
  transition: $transition;
  
  &.instagram {
    background: linear-gradient(45deg, #f093fb 0%, #f5576c 100%);
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 87, 108, 0.4);
    }
  }
  
  &.linkedin {
    background-color: #0077b5;
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 119, 181, 0.4);
    }
  }
  
  &.website {
    background-color: $neutral;
    color: white;
    
    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(74, 101, 131, 0.4);
    }
  }
}

// Informações extras
.extra-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  min-width: 120px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 11px;
  color: $text-secondary;
  
  i {
    color: $primary;
    width: 12px;
  }
  
  span {
    font-weight: 500;
  }
}


.stage-badge {
  padding: 4px 10px;
  border-radius: 16px;
  font-size: 11px;
  font-weight: 600;
  background-color: $gray-lighter;
  white-space: nowrap;
  
  &.prospecção {
    background-color: #e3f2fd;
    color: #1976d2;
  }
  
  &.qualificação {
    background-color: #e8f5e9;
    color: #388e3c;
  }
  
  &.objeção {
    background-color: #fff8e1;
    color: #f57f17;
  }
  
  &.fechamento {
    background-color: #e8eaf6;
    color: #3949ab;
  }
}

.score-badge {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: 600;
  color: white;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
  border: 2px solid white;
}

// Ações rápidas compactas
.lead-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.action-button {
  width: 36px;
  height: 36px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
  cursor: pointer;
  transition: $transition;
  font-size: 16px;
  
  &.phone {
    background-color: #e8f5e9;
    color: #27ae60;
    
    &:hover {
      background-color: #27ae60;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.email {
    background-color: #e3f2fd;
    color: #3498db;
    
    &:hover {
      background-color: #3498db;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.whatsapp {
    background-color: #e8f5e9;
    color: #25d366;
    
    &:hover {
      background-color: #25d366;
      color: white;
      transform: translateY(-1px);
    }
  }
  
  &.calendar {
    background-color: #f3e5f5;
    color: #9b59b6;
    
    &:hover {
      background-color: #9b59b6;
      color: white;
      transform: translateY(-1px);
    }
  }
}

// Conteúdo principal
.crm-main-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sugestoes-panel {
  flex: 1;
  overflow: hidden;
}

.actions-panel {
  padding: 16px;
  display: flex;
  justify-content: center;
  border-top: 1px solid #e0e0e0;
  background-color: #f8f9fa;
  
  .btn-primary {
    color: white;
    background-color: #3b7ddd;
    border: none;
    padding: 10px 18px;
    font-weight: 500;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
    
    &:hover {
      background-color: #2d6ec0;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
      transform: translateY(-1px);
    }
    
    i {
      margin-right: 6px;
    }
  }
}

// Responsividade para layout condensado
@media (max-width: 1024px) {
  .lead-condensed-layout {
    gap: 12px;
  }
  
  .extra-info {
    display: none; // Oculta informações extras em telas menores
  }
}

@media (max-width: 768px) {
  .main-header {
    padding: 10px 16px;
  }
  
  .main-title {
    font-size: 18px;
  }
  
  .subtitle {
    font-size: 12px;
  }
  
  .lead-card-header {
    padding: 12px 16px;
  }
  
  .lead-condensed-layout {
    flex-wrap: wrap;
    gap: 10px;
  }
  
  .lead-main-info {
    min-width: 200px;
    order: 1;
  }
  
  .lead-avatar-section {
    order: 0;
  }
  
  .social-compact {
    order: 2;
  }
  
  .lead-actions {
    order: 3;
    width: 100%;
    justify-content: center;
  }
  
  .lead-details-row {
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .header-content {
    gap: 8px;
  }
  
  .header-icon {
    font-size: 20px;
  }
  
  .main-title {
    font-size: 16px;
  }
  
  .subtitle {
    display: none; // Oculta subtitle em telas muito pequenas
  }
  
  .lead-name {
    font-size: 16px;
  }
  
  .detail-item {
    font-size: 11px;
  }
  
  .social-compact {
    gap: 6px;
  }
  
  .social-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}
