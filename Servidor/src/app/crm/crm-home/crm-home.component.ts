import { Component, OnInit } from '@angular/core';
import { ConversasService, MensagemChat, DadosLead } from '../services/conversas.service';

@Component({
  selector: 'app-crm-home',
  templateUrl: './crm-home.component.html',
  styleUrls: ['./crm-home.component.scss']
})
export class CrmHomeComponent implements OnInit {
  // Etapa do funil atual para teste
  etapaAtual = 'Conectado';
  
  // Dados do lead atual
  dadosLeadAtual: DadosLead | undefined;

  constructor(private conversasService: ConversasService) {}

  ngOnInit(): void {
    // Subscreve para atualizações no contexto da conversa
    this.conversasService.contextoConversa$.subscribe(contexto => {
      if (contexto && contexto.dadosLead) {
        this.dadosLeadAtual = contexto.dadosLead;
      }
    });
    
    // Inicializa com algumas mensagens de exemplo após subscrever
    this.simularNovasMensagens();
  }

  /**
   * Simula o recebimento de novas mensagens para testar o componente de sugestões
   */
  simularNovasMensagens(): void {
    const horaAtual = new Date().toLocaleTimeString();
    
    // Alterna entre diferentes conjuntos de simulações de mensagens para testar
    // diferentes cenários e sugestões
    const cenarios = [
      // Cenário 1: Cliente interessado no preço
      {
        mensagens: [
          {
            texto: 'Olá, estou interessado no seu sistema de gestão. Quanto custa?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          },
          {
            texto: 'Olá! Obrigado pelo interesse. Nossos planos começam a partir de R$99/mês.',
            remetente: 'Eu',
            horario: horaAtual,
            tipo: 'saida' as const
          },
          {
            texto: 'Isso parece interessante. Vocês oferecem alguma demonstração?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          }
        ],
        contato: 'João Silva',
        telefone: '+5511987654321',
        etapa: 'Qualificação'
      },
      
      // Cenário 2: Cliente com objeção de preço
      {
        mensagens: [
          {
            texto: 'Obrigado pelas informações, mas achei um pouco caro para o meu negócio atual.',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          },
          {
            texto: 'Entendo sua preocupação com o investimento. Você já avaliou o retorno que pode ter?',
            remetente: 'Eu',
            horario: horaAtual,
            tipo: 'saida' as const
          },
          {
            texto: 'Ainda não calculei. Teria como me passar mais detalhes sobre isso?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          }
        ],
        contato: 'Maria Oliveira',
        telefone: '+5511976543210',
        etapa: 'Objeção'
      },
      
      // Cenário 3: Cliente pronto para fechar
      {
        mensagens: [
          {
            texto: 'Fiquei muito satisfeito com a demonstração. Como fazemos para avançar?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          },
          {
            texto: 'Ótimo! Posso enviar a proposta comercial e os próximos passos por e-mail.',
            remetente: 'Eu',
            horario: horaAtual,
            tipo: 'saida' as const
          },
          {
            texto: 'Perfeito. E quanto tempo leva para implementar o sistema?',
            remetente: 'Cliente',
            horario: horaAtual,
            tipo: 'entrada' as const
          }
        ],
        contato: 'Ricardo Mendes',
        telefone: '+5511955556666',
        etapa: 'Fechamento'
      }
    ];
    
    // Seleciona um cenário aleatoriamente
    const cenarioAtual = cenarios[Math.floor(Math.random() * cenarios.length)];
    
    // Cria dados de lead simulados para teste
    const dadosLeadSimulados = this.gerarDadosLeadSimulados(cenarioAtual.contato, cenarioAtual.etapa);
    
    // Atualiza o contexto da conversa com as mensagens simuladas e dados do lead
    this.conversasService.setContextoConversa({
      mensagens: cenarioAtual.mensagens,
      contatoAtual: cenarioAtual.contato,
      telefoneAtual: cenarioAtual.telefone,
      etapaFunil: cenarioAtual.etapa,
      dadosLead: dadosLeadSimulados
    });
    
    // Força a atualização local para garantir que os dados sejam exibidos
    this.dadosLeadAtual = dadosLeadSimulados;
    
    // Atualiza a etapa atual para o componente saber
    this.etapaAtual = cenarioAtual.etapa;
  }

  /**
   * Gera dados simulados de lead para testes
   */
  private gerarDadosLeadSimulados(nome: string, etapa: string): DadosLead {
    // Define diferentes perfis de lead baseados no nome
    const perfis = {
      'João Silva': {
        email: '<EMAIL>',
        cargo: 'Gerente de Operações',
        empresa: 'Pizzaria Bella Napoli',
        segmento: 'Alimentação',
        tamanhoEmpresa: 'Pequena' as const,
        localizacao: 'São Paulo, SP',
        instagram: '@bellanapoli_pizzaria',
        site: 'www.bellanapoli.com.br',
        dataPrimeiroContato: '10/05/2023',
        ultimaInteracao: new Date().toLocaleDateString(),
        origemLead: 'Site',
        scoreLead: 75,
        interessesProdutos: ['Cardápio Digital', 'Gestão de Pedidos'],
        proximoFollowUp: this.gerarDataFutura(3),
        historicoPropostas: 'Enviada proposta inicial em 15/05/2023',
        observacoes: 'Cliente demonstra interesse em automatizar atendimento'      
      },
      'Maria Oliveira': {
        email: '<EMAIL>',
        cargo: 'Proprietária',
        empresa: 'Confeitaria Doce Sabor',
        segmento: 'Alimentação',
        tamanhoEmpresa: 'Pequena' as const,
        localizacao: 'Rio de Janeiro, RJ',
        instagram: '@docesabor_confeitaria',
        linkedin: 'linkedin.com/in/mariaoliveira',
        site: 'www.docesabor.com.br',
        dataPrimeiroContato: '22/04/2023',
        ultimaInteracao: new Date().toLocaleDateString(),
        origemLead: 'Instagram',
        scoreLead: 60,
        interessesProdutos: ['Sistema de Delivery', 'Fidelização'],
        proximoFollowUp: this.gerarDataFutura(2),
        observacoes: 'Preocupada com custo-benefício'      
      },
      'Ricardo Mendes': {
        email: '<EMAIL>',
        cargo: 'Diretor',
        empresa: 'Restaurante Fusion',
        segmento: 'Alimentação',
        tamanhoEmpresa: 'Média' as const,
        localizacao: 'Belo Horizonte, MG',
        instagram: '@restaurantefusion',
        linkedin: 'linkedin.com/in/ricardomendes',
        site: 'www.restaurantefusion.com.br',
        dataPrimeiroContato: '03/06/2023',
        ultimaInteracao: new Date().toLocaleDateString(),
        origemLead: 'Indicação',
        scoreLead: 90,
        interessesProdutos: ['Sistema Completo', 'Integração PDV'],
        proximoFollowUp: this.gerarDataFutura(1),
        historicoPropostas: 'Apresentação realizada em 10/06/2023. Proposta enviada em 12/06/2023',
        observacoes: 'Cliente com alto potencial, já testou a solução concorrente'      
      }
    };
    
    // Seleciona o perfil correspondente ou cria um genérico
    const perfil = perfis[nome] || {
      email: `${nome.toLowerCase().replace(' ', '.')}@email.com`,
      cargo: 'Proprietário',
      empresa: 'Empresa Exemplo',
      segmento: 'Alimentação',
      tamanhoEmpresa: 'Pequena' as const,
      dataPrimeiroContato: new Date().toLocaleDateString(),
      ultimaInteracao: new Date().toLocaleDateString(),
      origemLead: 'WhatsApp',
      scoreLead: 50
    };
    
    // Adiciona nome, telefone e etapa ao perfil
    return {
      ...perfil,
      nome: nome,
      telefone: this.gerarTelefoneAleatorio(),
      etapaFunil: etapa
    };
  }

  /**
   * Gera uma data futura para simulação
   */
  private gerarDataFutura(diasAFrente: number): string {
    const data = new Date();
    data.setDate(data.getDate() + diasAFrente);
    return data.toLocaleDateString();
  }

  /**
   * Gera um número de telefone aleatório para simulação
   */
  private gerarTelefoneAleatorio(): string {
    const ddd = Math.floor(Math.random() * 89) + 11; // DDD entre 11 e 99
    const parte1 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos
    const parte2 = Math.floor(Math.random() * 9000) + 1000; // 4 dígitos
    return `+55${ddd}9${parte1}${parte2}`;
  }
  
  
  /**
   * Retorna uma cor baseada no score do lead
   */
  getCorDoScore(score: number | undefined): string {
    if (!score) return '#999'; // Cinza para score indefinido
    
    if (score >= 80) return '#2ecc71'; // Verde para score alto
    if (score >= 50) return '#f39c12'; // Amarelo para score médio
    return '#e74c3c'; // Vermelho para score baixo
  }
  
  /**
   * Formata o score para exibição
   */
  formatarScore(score: number | undefined): string {
    if (!score && score !== 0) return 'N/A';
    return `${score}%`;
  }
}
