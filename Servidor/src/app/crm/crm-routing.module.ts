import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';

// componentes
import { CrmHomeComponent } from './crm-home/crm-home.component';
import { TelaCrmLeadsComponent } from '../tela-crm-leads/tela-crm-leads.component';
import { LeadCrudComponent } from './lead-crud/lead-crud.component';
import { CrmEmpresaCrudComponent } from './crm-empresa-crud/crm-empresa-crud.component';

const routes: Routes = [
  { path: 'home', component: CrmHomeComponent },
  { path: 'index', component: LeadCrudComponent },
  { path: 'empresas', component: CrmEmpresaCrudComponent },
  { path: 'leads', component: TelaCrmLeadsComponent },
  { path: '', redirectTo: 'leads', pathMatch: 'full' }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class CrmRoutingModule { }
