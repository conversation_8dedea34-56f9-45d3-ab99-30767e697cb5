import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { ServerService } from '../../services/ServerService';

@Injectable()
export class LeadService extends ServerService {
  constructor(protected http: HttpClient) {
    super(http);
  }

  private endpoint = '/crm/leads';

  liste(params: any = {}): Promise<any> {
    return this.obtenha(this.endpoint, params);
  }

  selecione(id: number): Promise<any> {
    return this.obtenha(`${this.endpoint}/${id}`, {});
  }

  salveLead(lead: any): Promise<any> {
    if (lead.id) {
      // Atualizar lead existente com PUT
      return this.facaPut(`${this.endpoint}/${lead.id}`, lead);
    } else {
      // Criar novo lead com POST
      return this.facaPost(this.endpoint, lead);
    }
  }

  removaLead(id: number): Promise<any> {
    return this.remova(`${this.endpoint}/${id}`, {});
  }

  buscarDadosInstagram(username: string): Promise<any> {
    const params: any = { username };
    return this.obtenha(`${this.endpoint}/dadosig`, params);
  }
} 