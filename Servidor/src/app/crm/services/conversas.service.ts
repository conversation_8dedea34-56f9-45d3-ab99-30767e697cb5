import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';

export interface MensagemChat {
  texto: string;
  remetente: string;
  horario: string;
  tipo?: 'entrada' | 'saida';
}

export interface DadosLead {
  nome?: string;
  telefone?: string;
  email?: string;
  cargo?: string;
  empresa?: string;
  segmento?: string;
  tamanhoEmpresa?: 'Pequena' | 'Média' | 'Grande';
  localizacao?: string;
  instagram?: string;
  linkedin?: string;
  site?: string;
  outrasRedes?: string;
  etapaFunil?: string;
  dataPrimeiroContato?: string;
  ultimaInteracao?: string;
  origemLead?: string;
  scoreLead?: number;
  interessesProdutos?: string[];
  proximoFollowUp?: string;
  historicoPropostas?: string;
  observacoes?: string;
}

export interface ContextoConversa {
  mensagens: MensagemChat[];
  contatoAtual?: string;
  telefoneAtual?: string;
  etapaFunil?: string;
  tomConversa?: string;
  dadosLead?: DadosLead;
}

@Injectable({
  providedIn: 'root'
})
export class ConversasService {
  // BehaviorSubject para manter o estado atual do contexto da conversa
  private contextoConversaSubject = new BehaviorSubject<ContextoConversa>({
    mensagens: []
  });

  // Observable que outros componentes podem assinar
  public contextoConversa$: Observable<ContextoConversa> = this.contextoConversaSubject.asObservable();

  constructor() {
    // Ouvir eventos da extensão do Chrome
    this.configurarEventListener();
  }

  /**
   * Configura o listener para receber atualizações da extensão do Chrome
   */
  private configurarEventListener(): void {
    // Adiciona um event listener ao window para receber mensagens da extensão
    window.addEventListener('message', (event) => {
      // Verifica se a mensagem é do tipo que esperamos
      if (event.data && event.data.type === 'crm_conversa_atualizada') {
        console.log('Recebendo atualização de conversa do WhatsApp:', event.data.payload);
        this.atualizarContextoConversa(event.data.payload);
      }
      
      // Verifica se é uma atualização de contato selecionado
      if (event.data && event.data.tipo === 'SELECIONOU_CONTATO') {
        console.log('Contato selecionado no WhatsApp:', event.data.payload);
        
        const contextoAtual = this.contextoConversaSubject.getValue();
        const novoContexto = {
          ...contextoAtual,
          contatoAtual: event.data.payload.nome || contextoAtual.contatoAtual,
          telefoneAtual: event.data.payload.telefone || contextoAtual.telefoneAtual
        };
        
        this.contextoConversaSubject.next(novoContexto);
      }
    });
  }

  /**
   * Atualiza o contexto da conversa com novas mensagens
   */
  private atualizarContextoConversa(payload: any): void {
    const contextoAtual = this.contextoConversaSubject.getValue();
    
    // Atualiza o contexto com as novas mensagens e informações
    const novoContexto: ContextoConversa = {
      mensagens: payload.mensagens || contextoAtual.mensagens,
      contatoAtual: payload.contatoAtual || contextoAtual.contatoAtual,
      telefoneAtual: payload.telefoneAtual || contextoAtual.telefoneAtual,
      etapaFunil: payload.etapaFunil || contextoAtual.etapaFunil
    };

    // Emite o novo contexto para todos os assinantes
    this.contextoConversaSubject.next(novoContexto);
  }

  /**
   * Permite atualizar manualmente o contexto da conversa (para testes ou simulações)
   */
  public setContextoConversa(contexto: ContextoConversa): void {
    this.contextoConversaSubject.next(contexto);
  }

  /**
   * Adiciona uma nova mensagem ao contexto
   */
  public adicionarMensagem(mensagem: MensagemChat): void {
    const contextoAtual = this.contextoConversaSubject.getValue();
    const novasMensagens = [...contextoAtual.mensagens, mensagem];
    
    this.contextoConversaSubject.next({
      ...contextoAtual,
      mensagens: novasMensagens
    });
  }

  /**
   * Limpa todas as mensagens do contexto atual
   */
  public limparMensagens(): void {
    const contextoAtual = this.contextoConversaSubject.getValue();
    
    this.contextoConversaSubject.next({
      ...contextoAtual,
      mensagens: []
    });
  }

  /**
   * Retorna o contexto atual da conversa
   */
  public getContextoAtual(): ContextoConversa {
    return this.contextoConversaSubject.getValue();
  }
}