// Content script injetado nas páginas do Instagram
// Faz a ponte entre o Instagram e a extensão PromoKit
  
// Estado da conversa atual do Instagram
let contextoInstagram = {
  mensagens: [],
  usuarioAtual: '',
  perfilAtual: '',
  etapaFunil: 'Prospecção',
  tomConversa: 'Consultivo',
  plataforma: 'Instagram'
};

// Configuração para controle do iframe
const configIG = {
  initialized: false,
  iframeInjected: false,
  currentUrl: location.href
};

/**
 * Sistema de detecção de mudança de URL no Instagram
 */
function setupUrlChangeDetector() {
  let lastUrl = location.href;
  
  const handleUrlChange = () => {
    const currentUrl = location.href;
    if (currentUrl !== lastUrl) {
      alert('URL do Instagram mudou: ' + currentUrl);
      lastUrl = currentUrl;
      configIG.currentUrl = currentUrl;
      
      // Atualiza contexto e iframe
      onInstagramUrlChange(currentUrl);
    }
  };

  // Intercepta pushState e replaceState
  const originalPushState = history.pushState;
  const originalReplaceState = history.replaceState;

  history.pushState = function() {
    originalPushState.apply(history, arguments);
    setTimeout(handleUrlChange, 100); // pequeno delay para garantir que a mudança foi processada
  };

  history.replaceState = function() {
    originalReplaceState.apply(history, arguments);
    setTimeout(handleUrlChange, 100);
  };

  // Detecta navegação via botões voltar/avançar
  window.addEventListener('popstate', () => {
    setTimeout(handleUrlChange, 100);
  });

  // Detecta mudanças via MutationObserver (backup para SPAs complexas)
  const urlObserver = new MutationObserver(() => {
    handleUrlChange();
  });

  urlObserver.observe(document.querySelector('title') || document.head, {
    childList: true,
    subtree: true
  });

  console.log('Detector de mudança de URL do Instagram configurado');
}

/**
 * Processa mudanças de URL no Instagram
 */
function onInstagramUrlChange(newUrl) {
  console.log('Processando mudança de URL:', newUrl);
  
  // Analisa a URL atual
  const urlInfo = analyzeInstagramUrl(newUrl);
  console.log('Informações da URL:', urlInfo);
  
  // Atualiza contexto baseado na URL
  updateContextFromUrl(urlInfo);
  
  // Atualiza o iframe do PromoKit
  updatePromoKitIframe(urlInfo);
  
  // Re-detecta usuário se estivermos em uma conversa
  if (urlInfo.isDirectMessage) {
    setTimeout(() => {
      detectarUsuarioInstagram();
    }, 1000); // delay para permitir que o DOM carregue
  }
}

/**
 * Analisa a URL do Instagram e extrai informações relevantes
 */
function analyzeInstagramUrl(url) {
  const urlObj = new URL(url);
  const pathname = urlObj.pathname;
  
  return {
    fullUrl: url,
    pathname: pathname,
    isDirectMessage: pathname.includes('/direct/'),
    isProfile: pathname.startsWith('/p/') || pathname.match(/^\/[^\/]+\/?$/),
    isExplore: pathname.includes('/explore/'),
    isReels: pathname.includes('/reels/'),
    isStories: pathname.includes('/stories/'),
    conversationId: pathname.includes('/direct/t/') ? pathname.split('/direct/t/')[1]?.split('/')[0] : null,
    username: pathname.includes('/direct/t/') ? null : pathname.split('/')[1]?.split('/')[0]
  };
}

/**
 * Atualiza o contexto baseado na URL atual
 */
function updateContextFromUrl(urlInfo) {
  // Se mudou de conversa, limpa o contexto
  if (urlInfo.isDirectMessage && urlInfo.conversationId) {
    if (contextoInstagram.conversationId !== urlInfo.conversationId) {
      console.log('Mudança de conversa detectada, limpando contexto');
      contextoInstagram.mensagens = [];
      contextoInstagram.usuarioAtual = '';
      contextoInstagram.perfilAtual = '';
      contextoInstagram.conversationId = urlInfo.conversationId;
    }
  } else if (!urlInfo.isDirectMessage) {
    // Saiu das mensagens diretas
    contextoInstagram.conversationId = null;
  }
  
  // Atualiza informações da URL no contexto
  contextoInstagram.currentUrl = urlInfo.fullUrl;
  contextoInstagram.currentPage = urlInfo.pathname;
}

/**
 * Atualiza o iframe do PromoKit com informações da URL atual
 */
function updatePromoKitIframe(urlInfo) {
  const iframe = document.getElementById('promokit-iframe');
  if (!iframe) {
    console.log('Iframe não encontrado para atualização');
    return;
  }

  try {
    // Envia dados da URL atual para o iframe
    const messageData = {
      type: 'instagram_url_changed',
      urlInfo: urlInfo,
      contexto: contextoInstagram,
      timestamp: new Date().toISOString()
    };

    // Tenta enviar mensagem para o iframe
    iframe.contentWindow.postMessage(messageData, '*');
    console.log('Dados enviados para iframe PromoKit:', messageData);

    // Também envia para a página principal (se estiver numa página PromoKit)
    window.postMessage({
      tipo: 'INSTAGRAM_URL_CHANGED',
      payload: messageData
    }, '*');

  } catch (error) {
    console.log('Erro ao atualizar iframe:', error);
  }
}

// 1. Mensagens vindas do background / sidepanel → página Instagram
chrome.runtime.onMessage.addListener((msg) => {
  console.log('Mensagem recebida no content-instagram:', msg);
  
  // Quando um usuário é selecionado no Instagram
  if (msg.type === 'SELECIONOU_USUARIO_INSTAGRAM') {
    // Atualiza informações do usuário no contexto
    contextoInstagram.usuarioAtual = msg.payload.nome || '';
    contextoInstagram.perfilAtual = msg.payload.perfil || '';
    
    // Reenvia para o contexto da página
    window.postMessage({ tipo: 'SELECIONOU_USUARIO_INSTAGRAM', payload: msg.payload }, '*');
    
    // Também envia o contexto atualizado para o CRM
    enviarContextoInstagramParaCRM();
  }
  
  // Quando novas mensagens são detectadas no Instagram
  if (msg.type === 'NOVAS_MENSAGENS_INSTAGRAM') {
    // Atualiza o histórico de mensagens no contexto
    if (msg.payload && Array.isArray(msg.payload.mensagens)) {
      contextoInstagram.mensagens = msg.payload.mensagens;
      enviarContextoInstagramParaCRM();
    }
  }

  // Mensagem para atualização manual do iframe
  if (msg.type === 'UPDATE_IFRAME') {
    const urlInfo = analyzeInstagramUrl(location.href);
    updatePromoKitIframe(urlInfo);
  }
});

// 2. Mensagens da página → background (se necessário)
window.addEventListener('message', (e) => {
  if (!e.data || !e.data.tipo) return;
  
  // Página quer solicitar algo à extensão
  if (e.data.tipo === 'INSTAGRAM_TO_EXTENSION') {
    chrome.runtime.sendMessage(e.data.msg);
  }
  
  // Atualização da etapa do funil ou tom da conversa
  if (e.data.tipo === 'ATUALIZAR_CONTEXTO_INSTAGRAM') {
    if (e.data.payload) {
      // Atualiza apenas os campos fornecidos
      if (e.data.payload.etapaFunil) {
        contextoInstagram.etapaFunil = e.data.payload.etapaFunil;
      }
      if (e.data.payload.tomConversa) {
        contextoInstagram.tomConversa = e.data.payload.tomConversa;
      }
      console.log('Contexto Instagram atualizado:', contextoInstagram);
    }
  }
  
  // Requisição para enviar mensagem para o Instagram
  if (e.data.tipo === 'ENVIAR_MENSAGEM_INSTAGRAM') {
    if (e.data.payload && e.data.payload.texto) {
      chrome.runtime.sendMessage({
        tipo: 'ENVIAR_TEXTO_INSTAGRAM',
        texto: e.data.payload.texto
      });
      
      // Adiciona a mensagem enviada ao contexto
      const novaMensagem = {
        texto: e.data.payload.texto,
        remetente: 'Eu',
        horario: new Date().toLocaleTimeString(),
        tipo: 'saida',
        plataforma: 'Instagram'
      };
      contextoInstagram.mensagens.push(novaMensagem);
      enviarContextoInstagramParaCRM();
    }
  }
});

/**
 * Função que envia o contexto atual da conversa do Instagram para o CRM
 */
function enviarContextoInstagramParaCRM() {
  window.postMessage({
    type: 'crm_conversa_instagram_atualizada',
    payload: contextoInstagram
  }, '*');
  console.log('Contexto Instagram enviado para CRM:', contextoInstagram);
}

/**
 * Função que coleta mensagens do DOM do Instagram
 * Será chamada periodicamente para monitorar novas mensagens
 */
function coletarMensagensInstagram() {
  try {
    // Verifica se estamos na página de mensagens do Instagram
    const mensagensContainer = document.querySelector('[role="main"]');
    if (!mensagensContainer) return;

    // Procura por mensagens de conversa
    const mensagens = document.querySelectorAll('[data-testid="message"]');
    if (mensagens.length === 0) return;

    const mensagensColetadas = [];
    mensagens.forEach((elemento, index) => {
      const texto = elemento.textContent?.trim();
      const isOutgoing = elemento.closest('[data-testid="message-outgoing"]') !== null;
      
      if (texto) {
        mensagensColetadas.push({
          texto: texto,
          remetente: isOutgoing ? 'Eu' : contextoInstagram.usuarioAtual || 'Usuário',
          horario: new Date().toLocaleTimeString(),
          tipo: isOutgoing ? 'saida' : 'entrada',
          plataforma: 'Instagram',
          indice: index
        });
      }
    });

    // Atualiza apenas se há novas mensagens
    if (mensagensColetadas.length !== contextoInstagram.mensagens.length) {
      contextoInstagram.mensagens = mensagensColetadas;
      enviarContextoInstagramParaCRM();
    }
    
  } catch (error) {
    console.log('Erro ao coletar mensagens do Instagram:', error);
  }
}

/**
 * Função que detecta quando um usuário é selecionado no Instagram
 */
function detectarUsuarioInstagram() {
  try {
    // Procura pelo nome do usuário na conversa ativa
    const nomeUsuario = document.querySelector('header [data-testid="thread-details-header-name"]');
    if (nomeUsuario) {
      const nome = nomeUsuario.textContent?.trim();
      if (nome && nome !== contextoInstagram.usuarioAtual) {
        contextoInstagram.usuarioAtual = nome;
        contextoInstagram.perfilAtual = `@${nome}`;
        
        // Notifica sobre a mudança de usuário
        window.postMessage({
          tipo: 'SELECIONOU_USUARIO_INSTAGRAM',
          payload: {
            nome: nome,
            perfil: contextoInstagram.perfilAtual
          }
        }, '*');
        
        enviarContextoInstagramParaCRM();
        console.log('Usuário Instagram detectado:', nome);

        // Atualiza iframe com novo usuário
        const urlInfo = analyzeInstagramUrl(location.href);
        updatePromoKitIframe(urlInfo);
      }
    }
  } catch (error) {
    console.log('Erro ao detectar usuário do Instagram:', error);
  }
}

// Inicia o monitoramento de mensagens e usuários (a cada 3 segundos)
setInterval(() => {
  detectarUsuarioInstagram();
  coletarMensagensInstagram();
}, 3000);

// Observer para detectar mudanças na página do Instagram
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
      // Verifica se novas mensagens foram adicionadas
      setTimeout(() => {
        detectarUsuarioInstagram();
        coletarMensagensInstagram();
      }, 500);
    }
  });
});

// Inicia o observer quando a página carregar
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', () => {
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
  });
} else {
  observer.observe(document.body, {
    childList: true,
    subtree: true
  });
}

console.log('Instagram content-script carregado e bridge estabelecida.');

/**
 * Aguarda o carregamento básico do Instagram (existência do main ou header)
 */
function waitForInstagramLoad() {
  return new Promise((resolve) => {
    const isLoaded = () => {
      return document.querySelector('[role="main"], header, nav');
    };

    if (isLoaded()) {
      resolve();
      return;
    }

    const interval = setInterval(() => {
      if (isLoaded()) {
        clearInterval(interval);
        resolve();
      }
    }, 1000);

    // Fallback para seguir em frente após 30 s mesmo se não detectar (evita travar)
    setTimeout(() => {
      clearInterval(interval);
      resolve();
    }, 30000);
  });
}

/**
 * Injeta o iframe do PromoKit na interface do Instagram
 */
function injectInstagramIframe() {
  if (configIG.iframeInjected) return;

  // Cria o container principal
  const container = document.createElement('div');
  container.id = 'promokit-iframe-container';
  container.className = 'promokit-iframe-container';

  // Inicia com z-index baixo para não cobrir diálogos
  container.style.zIndex = '99';

  // Cria botão de configurações
  const settingsBtn = document.createElement('button');
  settingsBtn.className = 'whatsapp-support-iframe-settings-btn';
  settingsBtn.title = 'Configurar URL do iframe';
  settingsBtn.innerHTML = '<svg viewBox="0 0 24 24"><path d="M19.14,12.94c0.04-0.3,0.06-0.61,0.06-0.94c0-0.32-0.02-0.64-0.07-0.94l2.03-1.58c0.18-0.14,0.23-0.41,0.12-0.61 l-1.92-3.32c-0.12-0.22-0.37-0.29-0.59-0.22l-2.39,0.96c-0.5-0.38-1.03-0.7-1.62-0.94L14.4,2.81c-0.04-0.24-0.24-0.41-0.48-0.41 h-3.84c-0.24,0-0.43,0.17-0.47,0.41L9.25,5.35C8.66,5.59,8.12,5.92,7.63,6.29L5.24,5.33c-0.22-0.08-0.47,0-0.59,0.22L2.74,8.87 C2.62,9.08,2.66,9.34,2.86,9.48l2.03,1.58C4.84,11.36,4.8,11.69,4.8,12s0.02,0.64,0.07,0.94l-2.03,1.58 c-0.18,0.14-0.23-0.41-0.12-0.61l1.92,3.32c0.12,0.22,0.37,0.29,0.59,0.22l2.39-0.96c0.5,0.38,1.03,0.7,1.62,0.94l0.36,2.54 c0.05,0.24,0.24,0.41,0.48,0.41h3.84c0.24,0,0.44-0.17,0.47-0.41l0.36-2.54c0.59-0.24,1.13-0.56,1.62-0.94l2.39,0.96 c0.22,0.08,0.47,0,0.59-0.22l1.92-3.32c0.12-0.22,0.07-0.47-0.12-0.61L19.14,12.94z M12,15.6c-1.98,0-3.6-1.62-3.6-3.6 s1.62-3.6,3.6-3.6s3.6,1.62,3.6,3.6S13.98,15.6,12,15.6z"/></svg>';
  settingsBtn.addEventListener('click', () => {
    const iframe = document.getElementById('promokit-iframe');
    if (iframe) {
      iframe.src = chrome.runtime.getURL('iframe-settings.html');
    }
  });
  container.appendChild(settingsBtn);

  // Cria iframe interno
  const iframe = document.createElement('iframe');
  iframe.id = 'promokit-iframe';
  iframe.sandbox = 'allow-scripts allow-same-origin allow-forms allow-popups';

  chrome.storage.local.get(['iframeCustomUrl'], (result) => {
    iframe.src = result.iframeCustomUrl || chrome.runtime.getURL('iframe-settings.html');
    
    // Depois que o iframe carrega, envia a URL atual
    iframe.onload = () => {
      setTimeout(() => {
        const urlInfo = analyzeInstagramUrl(location.href);
        updatePromoKitIframe(urlInfo);
      }, 1000);
    };
  });

  iframe.style.width = '100%';
  iframe.style.height = '100%';
  iframe.style.border = 'none';
  container.appendChild(iframe);

  // Resizer para ajustar largura
  const resizer = document.createElement('div');
  resizer.id = 'promokit-iframe-resizer';
  resizer.className = 'promokit-iframe-resizer';
  resizer.title = 'Arraste para ajustar a largura do painel';
  container.appendChild(resizer);

  // Aplica estilos principais via JS para evitar dependência extra de CSS
  const styleEl = document.createElement('style');
  styleEl.textContent = `
    .promokit-iframe-container {
      position: fixed;
      top: 0;
      right: 0;
      width: 400px;
      height: 100%;
      background: #ffffff;
      border-left: 1px solid #ccc;
      box-shadow: 0 0 4px rgba(0,0,0,0.3);
      transition: width 0.2s ease;
    }
    .promokit-iframe-resizer {
      position: absolute;
      top: 0;
      left: 0;
      width: 4px;
      height: 100%;
      cursor: col-resize;
      background: transparent;
    }
    .promokit-iframe-resizer:hover {
      background: rgba(0,0,0,0.1);
    }
  `;
  document.head.appendChild(styleEl);

  // Adiciona lógica de resize
  addResizeLogicInstagram(container, resizer);

  // Adiciona ao DOM
  document.body.appendChild(container);

  configIG.iframeInjected = true;
  console.log('Iframe PromoKit injetado no Instagram');
}

/**
 * Lógica de redimensionamento (adaptada)
 */
function addResizeLogicInstagram(container, resizer) {
  let isResizing = false;
  let startX = 0;
  let startWidth = 0;
  const minWidth = 150;
  const maxWidth = 800;

  resizer.addEventListener('mousedown', (e) => {
    if (e.button !== 0) return;
    isResizing = true;
    startX = e.clientX;
    startWidth = parseInt(document.defaultView.getComputedStyle(container).width, 10);
    document.body.style.userSelect = 'none';
    document.addEventListener('mousemove', onMouseMove);
    document.addEventListener('mouseup', onMouseUp);
    e.preventDefault();
  });

  function onMouseMove(e) {
    if (!isResizing) return;
    const diffX = startX - e.clientX;
    let newWidth = startWidth + diffX;
    newWidth = Math.max(minWidth, Math.min(newWidth, maxWidth));
    container.style.width = `${newWidth}px`;
    updateInstagramLayout(container);
  }

  function onMouseUp() {
    if (!isResizing) return;
    isResizing = false;
    document.body.style.userSelect = '';
    document.removeEventListener('mousemove', onMouseMove);
    document.removeEventListener('mouseup', onMouseUp);
    const finalWidth = parseInt(container.style.width, 10);
    localStorage.setItem('instagramSupportIframeWidth', finalWidth.toString());
  }

  // Aplica largura salva
  const saved = localStorage.getItem('instagramSupportIframeWidth');
  if (saved) {
    const w = Math.max(minWidth, Math.min(parseInt(saved, 10), maxWidth));
    container.style.width = `${w}px`;
  }
  updateInstagramLayout(container);
}

/**
 * Ajusta o layout da página para acomodar o iframe
 */
function updateInstagramLayout(container) {
  const width = parseInt(container.style.width || '400', 10);
  // Ajusta margem do body para não cobrir conteúdo
  document.body.style.setProperty('margin-right', `${width}px`, 'important');
}

/**
 * Processo de inicialização geral
 */
async function initializeInstagramSupport() {
  if (configIG.initialized) return;
  
  await waitForInstagramLoad();
  
  // Configura detector de URL antes de injetar iframe
  setupUrlChangeDetector();
  
  // Injeta iframe
  injectInstagramIframe();
  
  // Envia URL inicial após tudo estar configurado
  setTimeout(() => {
    const urlInfo = analyzeInstagramUrl(location.href);
    updatePromoKitIframe(urlInfo);
  }, 2000);
  
  configIG.initialized = true;
}

// Inicia após carregar script
initializeInstagramSupport(); 